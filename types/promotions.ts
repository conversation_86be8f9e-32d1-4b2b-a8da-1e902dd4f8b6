// types/promotions.ts
import { Types } from 'mongoose';

// Coupon and promotion types
export type CouponType = 'percentage' | 'fixed_amount' | 'free_shipping' | 'buy_x_get_y' | 'bulk_discount';
export type PromotionType = 'flash_sale' | 'seasonal' | 'category_discount' | 'group_bonus' | 'referral' | 'loyalty';
export type DiscountTarget = 'order_total' | 'specific_products' | 'category' | 'shipping' | 'group_orders';
export type CouponStatus = 'active' | 'inactive' | 'expired' | 'used_up' | 'scheduled';

// Base coupon interface
export interface Coupon {
  _id: string;
  code: string;
  name: string;
  description: string;
  type: CouponType;
  discountTarget: DiscountTarget;
  discountValue: number; // percentage or fixed amount
  minimumOrderValue?: number;
  maximumDiscountAmount?: number;
  usageLimit: number;
  usageCount: number;
  userUsageLimit: number; // per user
  validFrom: Date;
  validUntil: Date;
  status: CouponStatus;
  isPublic: boolean;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  groupOrdersOnly?: boolean;
  minimumGroupSize?: number;
  stackable: boolean;
  priority: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Database model interface
export interface ICoupon extends Coupon {
  _id: Types.ObjectId;
  createdBy: Types.ObjectId;
  applicableProducts?: Types.ObjectId[];
  applicableCategories?: Types.ObjectId[];
  excludedProducts?: Types.ObjectId[];
  excludedCategories?: Types.ObjectId[];
}

// Promotion campaign interface
export interface PromotionCampaign {
  _id: string;
  name: string;
  description: string;
  type: PromotionType;
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
  startDate: Date;
  endDate: Date;
  priority: number;
  budget?: number;
  spentAmount: number;
  targetAudience: {
    userSegments?: string[];
    locations?: string[];
    ageGroups?: string[];
    purchaseHistory?: {
      minimumOrders?: number;
      minimumSpent?: number;
      categories?: string[];
    };
  };
  rules: {
    triggerConditions: Array<{
      type: 'cart_value' | 'product_quantity' | 'category_purchase' | 'user_action' | 'time_based';
      operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'between';
      value: any;
      additionalValue?: any;
    }>;
    rewards: Array<{
      type: 'coupon' | 'points' | 'free_product' | 'upgrade' | 'cashback';
      value: any;
      conditions?: any;
    }>;
  };
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
    participantCount: number;
  };
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Referral system interface
export interface ReferralProgram {
  _id: string;
  name: string;
  description: string;
  isActive: boolean;
  referrerReward: {
    type: 'coupon' | 'points' | 'cashback' | 'credit';
    value: number;
    conditions?: {
      minimumRefereeSpend?: number;
      validityDays?: number;
    };
  };
  refereeReward: {
    type: 'coupon' | 'points' | 'discount' | 'credit';
    value: number;
    conditions?: {
      minimumOrderValue?: number;
      validityDays?: number;
    };
  };
  tierSystem?: {
    enabled: boolean;
    tiers: Array<{
      name: string;
      requiredReferrals: number;
      bonusMultiplier: number;
      additionalRewards?: any[];
    }>;
  };
  trackingPeriod: number; // days
  maxReferralsPerUser?: number;
  createdAt: Date;
  updatedAt: Date;
}

// User referral tracking
export interface UserReferral {
  _id: string;
  referrerId: string;
  refereeId: string;
  referralCode: string;
  programId: string;
  status: 'pending' | 'completed' | 'rewarded' | 'expired';
  refereeFirstPurchase?: {
    orderId: string;
    amount: number;
    date: Date;
  };
  rewards: {
    referrer?: {
      type: string;
      value: number;
      rewardedAt?: Date;
      couponCode?: string;
    };
    referee?: {
      type: string;
      value: number;
      rewardedAt?: Date;
      couponCode?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

// Loyalty program interface
export interface LoyaltyProgram {
  _id: string;
  name: string;
  description: string;
  isActive: boolean;
  pointsSystem: {
    earningRules: Array<{
      action: 'purchase' | 'signup' | 'review' | 'referral' | 'social_share' | 'birthday';
      pointsPerAction: number;
      conditions?: {
        minimumAmount?: number;
        categories?: string[];
        multiplier?: number;
      };
    }>;
    redemptionRules: Array<{
      rewardType: 'discount' | 'free_product' | 'free_shipping' | 'cashback';
      pointsCost: number;
      rewardValue: number;
      conditions?: any;
    }>;
  };
  tiers: Array<{
    name: string;
    requiredPoints: number;
    benefits: Array<{
      type: 'discount_multiplier' | 'free_shipping' | 'early_access' | 'exclusive_products';
      value: any;
    }>;
    color: string;
    icon?: string;
  }>;
  expirationPolicy: {
    enabled: boolean;
    expirationPeriod: number; // months
    warningPeriod: number; // days before expiration
  };
  createdAt: Date;
  updatedAt: Date;
}

// User loyalty tracking
export interface UserLoyalty {
  _id: string;
  userId: string;
  programId: string;
  totalPoints: number;
  availablePoints: number;
  currentTier: string;
  tierProgress: {
    currentPoints: number;
    nextTierPoints: number;
    progressPercentage: number;
  };
  pointsHistory: Array<{
    action: string;
    points: number;
    description: string;
    orderId?: string;
    expiresAt?: Date;
    createdAt: Date;
  }>;
  redemptionHistory: Array<{
    rewardType: string;
    pointsUsed: number;
    rewardValue: number;
    couponCode?: string;
    redeemedAt: Date;
  }>;
  lastActivity: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Coupon usage tracking
export interface CouponUsage {
  _id: string;
  couponId: string;
  userId: string;
  orderId: string;
  discountAmount: number;
  orderTotal: number;
  usedAt: Date;
}

// Promotion analytics
export interface PromotionAnalytics {
  campaignId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalImpressions: number;
    totalClicks: number;
    totalConversions: number;
    conversionRate: number;
    totalRevenue: number;
    totalDiscount: number;
    roi: number;
    participantCount: number;
    averageOrderValue: number;
    newCustomers: number;
    returningCustomers: number;
  };
  topPerformingCoupons: Array<{
    couponCode: string;
    usageCount: number;
    discountAmount: number;
    revenue: number;
  }>;
  userSegmentPerformance: Array<{
    segment: string;
    participants: number;
    conversionRate: number;
    revenue: number;
  }>;
  dailyBreakdown: Array<{
    date: Date;
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
}

// API request/response types
export interface CreateCouponRequest {
  code?: string; // auto-generated if not provided
  name: string;
  description: string;
  type: CouponType;
  discountTarget: DiscountTarget;
  discountValue: number;
  minimumOrderValue?: number;
  maximumDiscountAmount?: number;
  usageLimit: number;
  userUsageLimit: number;
  validFrom: Date;
  validUntil: Date;
  isPublic: boolean;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  groupOrdersOnly?: boolean;
  minimumGroupSize?: number;
  stackable: boolean;
  priority: number;
}

export interface CreateCouponResponse {
  success: boolean;
  coupon?: Coupon;
  error?: string;
}

export interface ValidateCouponRequest {
  code: string;
  userId: string;
  cartItems: Array<{
    productId: string;
    quantity: number;
    price: number;
    category: string;
  }>;
  orderTotal: number;
  isGroupOrder?: boolean;
  groupSize?: number;
}

export interface ValidateCouponResponse {
  valid: boolean;
  coupon?: Coupon;
  discountAmount?: number;
  error?: string;
  warnings?: string[];
}

export interface ApplyCouponRequest extends ValidateCouponRequest {
  orderId?: string;
}

export interface ApplyCouponResponse {
  success: boolean;
  discountAmount?: number;
  finalTotal?: number;
  error?: string;
}

export interface CreatePromotionRequest {
  name: string;
  description: string;
  type: PromotionType;
  startDate: Date;
  endDate: Date;
  priority: number;
  budget?: number;
  targetAudience: PromotionCampaign['targetAudience'];
  rules: PromotionCampaign['rules'];
}

export interface CreatePromotionResponse {
  success: boolean;
  campaign?: PromotionCampaign;
  error?: string;
}

export interface GenerateReferralCodeRequest {
  userId: string;
  programId: string;
}

export interface GenerateReferralCodeResponse {
  success: boolean;
  referralCode?: string;
  referralLink?: string;
  error?: string;
}

export interface ProcessReferralRequest {
  referralCode: string;
  refereeId: string;
}

export interface ReferralStatsRequest {
  userId: string;
}

export interface ReferralStatsResponse {
  success: boolean;
  stats?: {
    totalReferrals: number;
    successfulReferrals: number;
    pendingReferrals: number;
    totalEarnings: number;
    totalPoints: number;
    conversionRate: number;
    referralHistory: Array<{
      refereeId: string;
      refereeName: string;
      status: string;
      joinedAt: Date;
      firstPurchaseAt?: Date;
      earnedPoints: number;
      earnedAmount: number;
    }>;
  };
  error?: string;
}

export interface ShareReferralRequest {
  userId: string;
  platform: 'whatsapp' | 'facebook' | 'twitter' | 'instagram' | 'email' | 'copy_link';
  referralCode: string;
}

export interface ShareReferralResponse {
  success: boolean;
  shareUrl?: string;
  message?: string;
  error?: string;
}

export interface WithdrawalRequest {
  userId: string;
  amount: number;
  pointsUsed: number;
  method: 'bank_transfer' | 'mobile_money' | 'cash_pickup' | 'crypto' | 'paypal';
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    branchCode?: string;
    swiftCode?: string;
  };
  mobileMoneyDetails?: {
    phoneNumber: string;
    provider: 'vodacom' | 'mtn' | 'cell_c' | 'telkom';
  };
  cryptoDetails?: {
    walletAddress: string;
    currency: 'BTC' | 'ETH' | 'USDT' | 'USDC';
    network?: string;
  };
  paypalDetails?: {
    email: string;
  };
  pickupLocation?: string;
  priority: 'standard' | 'express' | 'instant';
  scheduledDate?: string;
}

// Premium Referral System Types
export interface ReferralTier {
  id: string;
  name: string;
  minReferrals: number;
  bonusMultiplier: number;
  perks: string[];
  badgeColor: string;
  exclusiveRewards: boolean;
}

export interface ReferralCampaign {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  bonusMultiplier: number;
  targetAudience: string[];
  isActive: boolean;
  maxParticipants?: number;
  currentParticipants: number;
}

export interface ReferralAnalytics {
  userId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  metrics: {
    totalClicks: number;
    uniqueClicks: number;
    conversions: number;
    conversionRate: number;
    revenue: number;
    topSources: Array<{
      source: string;
      clicks: number;
      conversions: number;
    }>;
    geographicData: Array<{
      country: string;
      clicks: number;
      conversions: number;
    }>;
    timeSeriesData: Array<{
      date: string;
      clicks: number;
      conversions: number;
      revenue: number;
    }>;
  };
}

export interface SmartRecommendation {
  type: 'audience' | 'timing' | 'content' | 'platform';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  actionable: boolean;
}

export interface ReferralLeaderboard {
  period: 'weekly' | 'monthly' | 'yearly' | 'all-time';
  entries: Array<{
    rank: number;
    userId: string;
    userName: string;
    avatar?: string;
    referrals: number;
    earnings: number;
    tier: string;
    badge?: string;
  }>;
}

export interface AutomatedCampaign {
  id: string;
  name: string;
  triggers: Array<{
    type: 'milestone' | 'date' | 'behavior' | 'tier_upgrade';
    condition: any;
  }>;
  actions: Array<{
    type: 'email' | 'sms' | 'push' | 'bonus' | 'reward';
    template: string;
    delay?: number;
  }>;
  isActive: boolean;
}

export interface ProcessReferralResponse {
  success: boolean;
  referral?: UserReferral;
  refereeReward?: {
    type: string;
    value: number;
    couponCode?: string;
  };
  error?: string;
}

export interface EarnLoyaltyPointsRequest {
  userId: string;
  action: string;
  orderId?: string;
  orderAmount?: number;
  additionalData?: {
    platform?: string;
    groupId?: string;
    groupName?: string;
    referredUserId?: string;
    referrerId?: string;
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    [key: string]: any;
  };
}

export interface EarnLoyaltyPointsResponse {
  success: boolean;
  pointsEarned?: number;
  totalPoints?: number;
  tierUpdate?: {
    previousTier: string;
    newTier: string;
    benefits: any[];
  };
  error?: string;
}

export interface RedeemLoyaltyPointsRequest {
  userId: string;
  rewardType: 'discount' | 'free_product' | 'free_shipping' | 'cashback' | 'cash_withdrawal' | 'delivery_payment';
  pointsCost: number;
  withdrawalDetails?: {
    amount: number;
    bankAccount?: string;
    method: 'bank_transfer' | 'mobile_money' | 'cash_pickup';
  };
}

export interface RedeemLoyaltyPointsResponse {
  success: boolean;
  couponCode?: string;
  rewardValue?: number;
  remainingPoints?: number;
  error?: string;
}

// Bulk operations
export interface BulkCouponGenerationRequest {
  template: Omit<CreateCouponRequest, 'code'>;
  quantity: number;
  codePrefix?: string;
  codeLength?: number;
}

export interface BulkCouponGenerationResponse {
  success: boolean;
  coupons?: Array<{ code: string; id: string }>;
  generatedCount?: number;
  error?: string;
}

// Promotion scheduling
export interface ScheduledPromotion {
  _id: string;
  campaignId: string;
  scheduledAt: Date;
  action: 'start' | 'pause' | 'resume' | 'stop';
  status: 'pending' | 'executed' | 'failed';
  executedAt?: Date;
  error?: string;
  createdAt: Date;
}

// A/B testing for promotions
export interface PromotionABTest {
  _id: string;
  name: string;
  description: string;
  variants: Array<{
    id: string;
    name: string;
    campaignId: string;
    trafficPercentage: number;
    isControl: boolean;
  }>;
  status: 'draft' | 'running' | 'completed' | 'paused';
  startDate: Date;
  endDate: Date;
  metrics: {
    [variantId: string]: {
      participants: number;
      conversions: number;
      revenue: number;
      conversionRate: number;
    };
  };
  winningVariant?: string;
  confidenceLevel: number;
  createdAt: Date;
  updatedAt: Date;
}
