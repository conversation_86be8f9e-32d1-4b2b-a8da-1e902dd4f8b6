"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Share2, 
  MessageCircle, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Mail, 
  Copy,
  Check,
  Users,
  Sparkles
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

interface GroupShareButtonProps {
  groupId: string;
  groupName: string;
  groupDescription?: string;
  memberCount?: number;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  showIcon?: boolean;
  className?: string;
}

interface ShareOption {
  name: string;
  icon: any;
  color: string;
  platform: 'whatsapp' | 'facebook' | 'twitter' | 'linkedin' | 'email' | 'copy';
  description: string;
}

export function GroupShareButton({
  groupId,
  groupName,
  groupDescription,
  memberCount,
  variant = "default",
  size = "default",
  showIcon = true,
  className = ""
}: GroupShareButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customMessage, setCustomMessage] = useState('');
  const [isSharing, setIsSharing] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState(false);

  const shareOptions: ShareOption[] = [
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'bg-green-500 hover:bg-green-600',
      platform: 'whatsapp',
      description: 'Share with friends and family'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600 hover:bg-blue-700',
      platform: 'facebook',
      description: 'Post to your timeline'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-sky-500 hover:bg-sky-600',
      platform: 'twitter',
      description: 'Tweet to your followers'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'bg-blue-700 hover:bg-blue-800',
      platform: 'linkedin',
      description: 'Share professionally'
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'bg-gray-600 hover:bg-gray-700',
      platform: 'email',
      description: 'Send via email'
    },
    {
      name: 'Copy Link',
      icon: copiedUrl ? Check : Copy,
      color: 'bg-purple-600 hover:bg-purple-700',
      platform: 'copy',
      description: 'Copy share link'
    }
  ];

  const defaultMessage = `🎉 Join my Stokvel group "${groupName}"! We're saving money together through bulk buying. ${memberCount ? `Already ${memberCount} members strong!` : ''} Join us and start saving today!`;

  const handleShare = async (platform: string) => {
    setIsSharing(true);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/groups/share', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          groupId,
          platform,
          customMessage: customMessage || defaultMessage
        })
      });

      const data = await response.json();
      
      if (data.success && data.shareUrl) {
        if (platform === 'copy') {
          // Copy to clipboard
          await navigator.clipboard.writeText(data.shareUrl);
          setCopiedUrl(true);
          toast.success('Share link copied to clipboard!');
          setTimeout(() => setCopiedUrl(false), 2000);
        } else {
          // Open share URL
          window.open(data.shareUrl, '_blank', 'width=600,height=400');
          toast.success(`${platform.charAt(0).toUpperCase() + platform.slice(1)} share opened!`);
        }
      } else {
        toast.error(data.error || 'Failed to generate share link');
      }
    } catch (error) {
      console.error('Error sharing group:', error);
      toast.error('Failed to share group');
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          className={className}
        >
          {showIcon && <Share2 className="h-4 w-4 mr-2" />}
          Share Group
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            Share "{groupName}"
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Group Info */}
          <Card className="bg-gradient-to-r from-primary/10 to-purple-500/10 border-primary/20">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-primary/20 rounded-lg">
                  <Sparkles className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-primary">{groupName}</h3>
                  {groupDescription && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {groupDescription}
                    </p>
                  )}
                  {memberCount && (
                    <p className="text-xs text-primary/80 mt-2">
                      {memberCount} members already saving together
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Custom Message */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Customize your message (optional)
            </label>
            <Textarea
              placeholder={defaultMessage}
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          {/* Share Options */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Choose platform:</h4>
            <div className="grid grid-cols-2 gap-3">
              {shareOptions.map((option, index) => (
                <motion.div
                  key={option.platform}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Button
                    onClick={() => handleShare(option.platform)}
                    disabled={isSharing}
                    className={`w-full h-auto p-3 ${option.color} text-white`}
                    variant="default"
                  >
                    <div className="flex items-center gap-2">
                      <option.icon className="h-5 w-5" />
                      <div className="text-left">
                        <div className="font-medium text-sm">{option.name}</div>
                        <div className="text-xs opacity-90">{option.description}</div>
                      </div>
                    </div>
                  </Button>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Benefits */}
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4">
              <h4 className="text-sm font-semibold text-green-800 mb-2">
                🎁 Earn rewards for sharing!
              </h4>
              <ul className="text-xs text-green-700 space-y-1">
                <li>• Get 50 points for each share</li>
                <li>• Earn 200 bonus points when someone joins</li>
                <li>• Build your referral network</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
